#!/usr/bin/env python3
"""
Test výkonu MCP serveru vs. původní verze
"""
import time
import requests
import json
from pathlib import Path

# Test adresář
TEST_DIR = r"C:\gz_projekt\data-for-testing\01"

def test_mcp_server():
    """Test MCP serveru přes HTTP API"""
    print("🧪 Testuji MCP server...")
    
    start_time = time.time()
    
    # Připravíme požadavek pro MCP server (FastMCP používá jiný formát)
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": "run_full_preflight_check",
            "arguments": {
                "source_directory": TEST_DIR
            }
        }
    }

    try:
        # Pro SSE endpoint použijeme /sse
        response = requests.post(
            "http://127.0.0.1:8050/sse",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5 minut timeout
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ MCP server dokončen za: {duration:.2f} sekund")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status: {response.status_code}")
            print(f"📊 Výsledek: {result.get('result', {}).get('status', 'unknown')}")
        else:
            print(f"❌ Chyba: {response.status_code}")
            print(f"📝 Odpověď: {response.text}")
            
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ Chyba po {duration:.2f} sekundách: {e}")

def test_original_app():
    """Test původní aplikace"""
    print("🧪 Testuji původní aplikaci...")
    
    # Import původní verze
    import sys
    sys.path.append(str(Path(__file__).parent / "src"))
    
    from vinyl_preflight_app import PreflightProcessor
    from dotenv import load_dotenv
    import os
    
    load_dotenv()
    API_KEY = os.getenv("OPENROUTER_API_KEY")
    
    def dummy_progress(value, maximum):
        pass
    
    def dummy_status(text):
        print(f"📝 {text}")
    
    start_time = time.time()
    
    try:
        processor = PreflightProcessor(API_KEY, dummy_progress, dummy_status)
        result = processor.run(TEST_DIR)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️ Původní aplikace dokončena za: {duration:.2f} sekund")
        print(f"📊 Výsledek: {result}")
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"❌ Chyba po {duration:.2f} sekundách: {e}")

if __name__ == "__main__":
    print("🚀 Porovnání výkonu MCP server vs. původní aplikace")
    print(f"📁 Test adresář: {TEST_DIR}")
    print("=" * 60)
    
    # Test MCP serveru
    test_mcp_server()
    
    print("=" * 60)
    
    # Test původní aplikace
    test_original_app()
    
    print("=" * 60)
    print("✅ Testy dokončeny!")
